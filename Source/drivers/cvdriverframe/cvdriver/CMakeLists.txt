cmake_minimum_required(VERSION 3.10)

PROJECT (drdriver)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} main.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drdriver)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)

SET(LINK_LIBS ACE drlog drlogimpl licverify License)
#FIXME: add this for debug mode
# SET(SPECOUTDIR /drivers/codesysdrv)
# SET(SPECOUTDIR /drivers/dsftxdrv)
# SET(SPECOUTDIR /drivers/modbus)

IF(UNIX)
	IF(HPUX)
		SET(LINK_LIBS ${LINK_LIBS} pthread)
	ENDIF(HPUX)
ELSE(UNIX)
	SET(LINK_LIBS ${LINK_LIBS} exceptionreport dbghelp)
ENDIF(UNIX)

INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonExec)

#ifndef UDP_SERVER_HANDLER_HXX
#define UDP_SERVER_HANDLER_HXX

#include <ace/SOCK_Dgram.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>
using namespace std;

class CUdpServerHandler : public CDeviceConnection
{
public:
	CUdpServerHandler(void);
	~CUdpServerHandler(void);
	virtual long Start();
	virtual long Stop();
	virtual long SetConnectParam(char *szConnParam);
	// �����豸
	virtual long Connect();
	// �Ͽ�����
	virtual long DisConnect();
	// ��������
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS);

	virtual bool IsConnected(){return true; } // ֻҪ�����Ѿ�new��������˵�����ӽ�������deleteʱ�������ͷ�
	virtual void ClearDeviceRecvBuffer();
protected:
	string	m_strDeviceName; 
	string m_strDeviceIP;				// �豸IP
	unsigned short m_ulDevicePort;
	unsigned short m_uLocalPort;
	ACE_SOCK_Dgram m_dgramSock; 
	ACE_INET_Addr m_remoteAddr;
};
#endif


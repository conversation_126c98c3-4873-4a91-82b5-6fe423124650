/**************************************************************
*  Filename:    SockHandler.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*
*  Description: SockHandler.h.
*
*  @author:     lijingjing
*  @version     05/29/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _TCPSERVER_HANDLER_H_
#define _TCPSERVER_HANDLER_H_

#include <ace/Svc_Handler.h>
#include <ace/SOCK_Connector.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>
using namespace std;

#define DEFAULT_INPUT_SIZE		66000	// ���ջ�����Ĭ�ϴ�С

typedef void (*PFN_CVCommConnCallBack)(int nConnected, long lParam, void* pCallbackParam, char *szPararm);
typedef void (*PFN_CVCommRecvCallBack)(char* pszRecvBuf, int nRecvBytes, long lParam, void* pCallbackParam, char *szPararm);

class CTaskGroup;
class CDevice;
class CTcpServerHandler : public ACE_Svc_Handler<ACE_SOCK_STREAM, ACE_MT_SYNCH>, public CDeviceConnection
{
public:
	
	typedef ACE_Svc_Handler<ACE_SOCK_STREAM, ACE_MT_SYNCH> super;
	
	CTcpServerHandler() ;
	virtual ~CTcpServerHandler();
	
public:
	virtual long Start();
	virtual long Stop();
	// ���Ӳ����������tcp�����:IP,Port,��ѡ�����ӳ�ʱ���Ժ��ǿ����д���
	// ����һ����tcpclient���ӷ�ʽ����ʽ: IP:***********;Port:502;ConnTimeOut:3000
	virtual long SetConnectParam(char *szConnParam);
	// �����豸
	virtual long Connect();
	// �Ͽ�����
	virtual long DisConnect();
	// ��������
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS);

	virtual bool IsConnected(){return true; } // ֻҪ�����Ѿ�new��������˵�����ӽ�������deleteʱ�������ͷ�
	virtual void ClearDeviceRecvBuffer();
protected:
	void OnConnectStateChange(bool bConnected);

public:
	virtual int open(void* p = 0);	
	// ��������ʱ�ú���������.
	virtual int handle_input(ACE_HANDLE fd = ACE_INVALID_HANDLE);
	 
	// �������ʱ�ú���������.
	virtual int handle_output(ACE_HANDLE fd = ACE_INVALID_HANDLE);
	
	// ��SockHandler��ACE_Reactor���Ƴ�ʱ�ú���������.
	virtual int handle_close(ACE_HANDLE handle, ACE_Reactor_Mask close_mask);

protected:
	string	m_strDeviceName; 
	string m_strDeviceIP;				// �豸IP
	unsigned short m_uDevicePort;
	CTaskGroup *m_pTaskGroup;
	CDevice		*m_pDevice;
	
protected:
	char *	m_szRecvBuf;
	PFN_CVCommRecvCallBack m_pfnRecvCallback;		// ���յ�����ʱ�Ļص�����
	PFN_CVCommConnCallBack m_pfnConnectCallback;		// ���ӻص�����

protected:
	long	m_lCallbackParam;				// �ص������ĵ�1������
	char	m_szCallbackParam[ICV_DEVICENAME_MAXLEN];	// �ص������ĵ�2������
	void*	m_pCallbackParam;				// �ص������ĵ�3������
};

#endif // _TCPSERVER_HANDLER_H_

#ifndef UDP_CLIENT_HANDLER_H_
#define UDP_CLIENT_HANDLER_H_

#include <ace/SOCK_Dgram.h>
#include <ace/SOCK_Dgram_Mcast.h>
#include <ace/INET_Addr.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>
#include <string.h>
using namespace std;

typedef void (*PFN_CVCommRecvCallBack)(char* pszRecvBuf, int nRecvBytes, long lParam, void* pCallbackParam, char *szPararm);

class CUdpClientHandler : public CDeviceConnection
{
public:
	CUdpClientHandler(void);
	~CUdpClientHandler(void);
	virtual long Start();
	virtual long Stop();
	virtual long SetConnectParam(char *szConnParam);
	// 连接设备
	virtual long Connect();
	// 断开连接
	virtual long DisConnect();
	// 发送数据
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS);

	virtual bool IsConnected() { return m_bSocketReady; } // UDP无连接，此处表示套接字是否就绪
	virtual void ClearDeviceRecvBuffer();

	void SetRecvCallBackFunc(PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam);

public:
	string	m_strDeviceName;

protected:
	string	m_strServerIP;			// 服务器IP地址
	unsigned short m_moduleno;		// 模块序号
	unsigned short m_port;			// 端口
	bool m_multicast;				// 0为单播，1为组播
	bool m_transfermode;			// 0为缓冲区刷新，1为多缓冲区
	bool m_bSocketReady;			// 套接字就绪状态（UDP无连接概念）

	// UDP套接字
	ACE_SOCK_Dgram* m_pUdpSocket;		// 单播套接字
	ACE_SOCK_Dgram_Mcast* m_pMcastSocket;	// 组播套接字
	ACE_INET_Addr m_remoteAddr;			// 远程地址
	ACE_INET_Addr m_localAddr;			// 本地地址

	// 回调函数相关
	PFN_CVCommRecvCallBack m_pfnRecvCallback;		// 接收数据时的回调函数
	long	m_lCallbackParam;				// 回调函数的第1个参数
	char	m_szCallbackParam[ICV_DEVICENAME_MAXLEN];	// 回调函数的第2个参数
	void*	m_pCallbackParam;				// 回调函数的第3个参数
};
#endif
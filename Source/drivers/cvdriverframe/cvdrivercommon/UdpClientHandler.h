#ifndef UDP_CLIENT_HANDLER_H_
#define UDP_CLIENT_HANDLER_H_

#include <ace/SOCK_Dgram.h>
#include <ace/SOCK_Dgram_Mcast.h>
#include <ace/INET_Addr.h>
#include <common/CVLog.h>
#include <common/cvdefine.h>
#include "common/SimpleQueue.h"
#include "DeviceConnection.h"
#include <string>
#include <string.h>
using namespace std;



class CUdpClientHandler : public CDeviceConnection
{
public:
	CUdpClientHandler(void);
	~CUdpClientHandler(void);
	virtual long Start();
	virtual long Stop();
	virtual long SetConnectParam(char *szConnParam);
	// 连接设备
	virtual long Connect();
	// 断开连接
	virtual long DisConnect();
	// 发送数据
	virtual long Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv(char* szRecvBuf, long nSendBytes, long lTimeoutMs);
	virtual long Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS);

	virtual bool IsConnected() { return true; } // UDP无连接，对象创建成功即可使用
	virtual void ClearDeviceRecvBuffer();

public:
	string	m_strDeviceName;

protected:
	string	m_strServerIP;			// 组播IP地址（仅组播模式使用）
	unsigned short m_moduleno;		// 模块序号
	unsigned short m_port;			// 本地监听端口
	bool m_multicast;				// 0为单播，1为组播
	bool m_transfermode;			// 0为缓冲区刷新，1为多缓冲区

	// UDP套接字
	ACE_SOCK_Dgram* m_pUdpSocket;		// 单播套接字
	ACE_SOCK_Dgram_Mcast* m_pMcastSocket;	// 组播套接字
	ACE_INET_Addr m_remoteAddr;			// 远程地址
	ACE_INET_Addr m_localAddr;			// 本地地址
};
#endif
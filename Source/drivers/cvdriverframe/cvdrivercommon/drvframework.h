#ifndef _Drv_FRAMEWORK_H_
#define _Drv_FRAMEWORK_H_

#define NULLASSTRING(x) x==NULL?"":x

#include <ace/Event_Handler.h>
#include <ace/OS_NS_strings.h>
#include <map>
#include <list>
#include <string>
#include "common/cvdefine.h"
#include "driversdk/cvdrivercommon.h"	// CONFIGHANDLE, DRVHANDLE
#include "tinyxml/tinyxml.h"
#include "string.h"

#define MAX_DRVOBJ_USERDATA_SIZE		20			// �û����ݵ������Ŀ
#define MAX_DRVOBJ_USERPTR_SIZE         20          //�û�ָ��������Ŀ

using namespace std;

extern string	g_strDrvPath;
extern string	g_strDrvName;

// typedef bool (*PFN_DDA_IsTagInBlock)(char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], CVDATABLOCK * pcvDataBlock, int32&nBitOffSet);
// typedef long (*PFN_DDA_ParseIoAddr)(char* szIoAddr,  char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], int32 & nLength);

typedef long (*PFN_Begin)();
typedef long (*PFN_Initialize)(DRVHANDLE hDriver);
typedef long (*PFN_OnDeviceAdd)(DRVHANDLE hCfgDevice);
typedef long (*PFN_OnDataBlockAdd)(DRVHANDLE hDevice, DRVHANDLE hDatablock);

typedef long (*PFN_OnDeviceDelete)(DRVHANDLE hCfgDevice);
typedef long (*PFN_OnDataBlockDelete)(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock);
// ���ɾ��������
typedef long (*PFN_OnDataBlockModify)(DRVHANDLE hDevice, DRVHANDLE hOldDrvDataBlock, DRVHANDLE hNewDrvDataBlock);

typedef long (*PFN_OnDeviceTimer)(DRVHANDLE hDevice);
typedef long (*PFN_OnDataBlockTimer)(DRVHANDLE hDevice, DRVHANDLE hBlk);
typedef long (*PFN_OnReadData)(DRVHANDLE hDevice, DRVHANDLE hBlk);
typedef long (*PFN_OnWriteCmd)(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits);
typedef long (*PFN_OnWriteCmdCIP)(DRVHANDLE hDevice, DRVHANDLE hDatablock,int nTagType, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits);
typedef long (*PFN_OnWriteCmdEx)(DRVHANDLE hDevice, DRVHANDLE hDatablock, string strTagName, string strTagAddr, int32 nTagID, uint8 nDataType, uint8 nTagType,int nTagByteOffset, int nTagBitOffset, char* szCmdData, int nCmdDataLenBits);

typedef long (*PFN_UnInitialize)();
typedef long (*PFN_GetDrvFrameVersion)();
typedef long (*PFN_TagsToGroups)(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum);
typedef long (*PFN_TagsToGroupsEx)(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum,
	DRVHANDLE hDevice, int nCurrentConnectionNum,unsigned int nTotalConnectionNum);

typedef long (*PFN_OnHeartBeat)();
typedef long (*PFN_OnBatchUpdateData)(int& nMsTimeOut ,int& nDataSize);
typedef bool (*PFN_IsTagInBlock)(char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], CVDATABLOCK * pcvDataBlock, int32&nBitOffSet);
typedef int32 (*PFN_ParseIoAddr)(char* szIoAddr,  char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], int32 & nLength);
extern PFN_Begin				g_pfnBegin;
extern PFN_Initialize			g_pfnInit;
extern PFN_OnDeviceAdd			g_pfnOnDeviceAdd;
extern PFN_OnDataBlockAdd		g_pfnOnDataBlockAdd;

extern PFN_OnDeviceDelete		g_pfnOnDeviceDelete;
extern PFN_OnDataBlockDelete	g_pfnOnDataBlockDelete;

extern PFN_OnDeviceTimer		g_pfnOnDeviceTimer;
extern PFN_OnDataBlockTimer		g_pfnOnDataBlockTimer;
extern PFN_OnReadData			g_pfnOnReadData;
extern PFN_OnWriteCmd			g_pfnOnWriteCmd;
extern PFN_OnWriteCmdEx			g_pfnOnWriteCmdEx;
extern PFN_OnWriteCmdCIP		g_pfnOnWriteCmdCIP;

extern PFN_UnInitialize			g_pfnUnInit;
extern PFN_GetDrvFrameVersion	g_pfnGetDrvFrameVersion;
extern PFN_TagsToGroups			g_pfnTagsToGroups;
extern PFN_TagsToGroupsEx		g_pfnTagsToGroupsEx;

extern PFN_OnHeartBeat			g_pfnOnHeartBeat;
extern PFN_OnBatchUpdateData	g_pfnOnBatchUpdateData;
extern PFN_IsTagInBlock         g_pfnIsTagInBlock;
extern PFN_ParseIoAddr         g_pfnParseIoAddr;

#define CONFIGOBJ_TYPE_DEVGROUP			0		// �豸��
#define CONFIGOBJ_TYPE_DEVICE			1		// �豸
#define CONFIGOBJ_TYPE_DATABLOCK		2		// ���ݿ�

// ������������Ļ��࣬�������豸�顢�豸�����ݿ飩
class CDrvObjectBase
{
public:
	CDrvObjectBase()
	{
		m_nDevObjectType = CONFIGOBJ_TYPE_DEVGROUP;
		memset(m_nUserDatas, 0, sizeof(m_nUserDatas));
		memset(m_szUserPtrs, 0, sizeof(m_szUserPtrs));
		m_bConfigChanged = true;
		m_strName = "";
		m_strDesc = "";
	}
	virtual ~CDrvObjectBase()
	{
		m_mapAttrNameToValue.clear();
	}
	virtual CDrvObjectBase &operator=(CDrvObjectBase &theDrvObj)
	{
		m_nDevObjectType = theDrvObj.m_nDevObjectType;
		for(int i = 0; i < sizeof(m_nUserDatas)/sizeof(int); i ++)
			m_nUserDatas[i] = theDrvObj.m_nUserDatas[i];

		for (int j = 0; j < sizeof(m_szUserPtrs)/sizeof(void *); j++)
			m_szUserPtrs[j] = theDrvObj.m_szUserPtrs[j];

		m_strName = theDrvObj.m_strName;
		m_strDesc = theDrvObj.m_strDesc;
		m_strParam1 = theDrvObj.m_strParam1;
		m_strParam2 = theDrvObj.m_strParam2;
		m_strParam3 = theDrvObj.m_strParam3;
		m_mapAttrNameToValue = theDrvObj.m_mapAttrNameToValue;
		m_bConfigChanged = theDrvObj.m_bConfigChanged;
		return *this;
	}

	//�ж�������Ϣ�Ƿ���ͬ���Ƚϸ������ԣ����������Ӷ���ıȽϡ�����һ��ȱʡʵ��
	virtual bool operator==( CDrvObjectBase &theDrvObjectBase)
	{
#ifdef _WIN32
		if(stricmp(theDrvObjectBase.m_strName.c_str(), m_strName.c_str()) != 0)
			return false;
		if(stricmp(theDrvObjectBase.m_strDesc.c_str(), m_strDesc.c_str()) != 0)
			return false;
		if(stricmp(theDrvObjectBase.m_strParam1.c_str(), m_strParam1.c_str()) != 0)
			return false;
		if(stricmp(theDrvObjectBase.m_strParam2.c_str(), m_strParam2.c_str()) != 0)
			return false;
		if(stricmp(theDrvObjectBase.m_strParam3.c_str(), m_strParam3.c_str()) != 0)
			return false;
#else
		if(strcasecmp(theDrvObjectBase.m_strName.c_str(), m_strName.c_str()) != 0)
			return false;
		if(strcasecmp(theDrvObjectBase.m_strDesc.c_str(), m_strDesc.c_str()) != 0)
			return false;
		if(strcasecmp(theDrvObjectBase.m_strParam1.c_str(), m_strParam1.c_str()) != 0)
			return false;
		if(strcasecmp(theDrvObjectBase.m_strParam2.c_str(), m_strParam2.c_str()) != 0)
			return false;
		if(strcasecmp(theDrvObjectBase.m_strParam3.c_str(), m_strParam3.c_str()) != 0)
			return false;
#endif // WIN32

		if(m_mapAttrNameToValue.size() != theDrvObjectBase.m_mapAttrNameToValue.size())
			return false;

		std::map<string, string>::iterator itAttr = m_mapAttrNameToValue.begin();
		std::map<string, string>::iterator itAttr2 = theDrvObjectBase.m_mapAttrNameToValue.begin();
		for(; itAttr != m_mapAttrNameToValue.end(); itAttr ++,itAttr2++)
		{
			if(itAttr->first.compare(itAttr2->first) != 0 || itAttr->second.compare(itAttr2->second) != 0)
				return false;
		}
		
		return true;
	}

	int LoadParamsFromXml(TiXmlElement *pNodeDrvObj)
	{
		if(!pNodeDrvObj)
			return -1;

		m_strName = NULLASSTRING(pNodeDrvObj->Attribute("name"));
		m_strDesc = NULLASSTRING(pNodeDrvObj->Attribute("desc"));
		m_strParam1 = NULLASSTRING(pNodeDrvObj->Attribute("param1"));
		m_strParam2 = NULLASSTRING(pNodeDrvObj->Attribute("param2"));
		m_strParam3 = NULLASSTRING(pNodeDrvObj->Attribute("param3"));

		//��ȡ�豸����������
		for (TiXmlAttribute *pAttribute =  pNodeDrvObj->FirstAttribute(); 
			pAttribute != NULL && pAttribute != pNodeDrvObj->LastAttribute(); 
			pAttribute = pAttribute->Next())
		{
			if (pAttribute->Value() != NULL)
				m_mapAttrNameToValue[pAttribute->Name()] = pAttribute->Value();
		}
		
		return DRV_SUCCESS;
	}
	void SetUserData( int iIndex, int lUserData );
	int GetUserData( int iIndex );
	void SetUserPtr( int iIndex, void *pUserPtr );
	void *GetUserPtr( int iIndex );
public:
	int		m_nDevObjectType;		// ���ݿ顢�豸�����豸������
	string	m_strParam1, m_strParam2, m_strParam3;// Ԥ������������
	string	m_strName;				// ����
	string	m_strDesc;		// ����
	std::map<string, string>	m_mapAttrNameToValue;	// ���ݿ�������ļ���ȡ�������Ժ�ֵ�Ե�ӳ��

public:
	int		m_nUserDatas[MAX_DRVOBJ_USERDATA_SIZE];
	void    *m_szUserPtrs[MAX_DRVOBJ_USERPTR_SIZE];
	bool	m_bConfigChanged;		// �Ƿ���Ϊ���߲����޸������ݿ飨����ʼ��ַ�����ȡ��ֽ����͡�˳��ȣ����豸��Ϣ���豸�鲻��Ҫ�жϣ���Ϊֻ���������岻��
};


/**
 *  ��run_reactor_event_loop�������룬�ڴ˺���whileѭ����sleep����ֹcpu����.
 *
 *  @return		int.
 *
 *  @version     10/16/2018  zhangqiang  Initial Version.
 */
int ReactorEventHook(ACE_Reactor *);
#endif // _Drv_FRAMEWORK_H_

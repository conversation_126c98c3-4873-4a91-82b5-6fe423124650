#include "UdpClientHandler.h"
#include "driversdk/cvdrivercommon.h"
#include "ace/Time_Value.h"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include <ace/ACE.h>
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "ace/Message_Block.h"

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
#define RECV_BUFFER_LEN 1024

extern string	g_strDrvName;
extern CCVLog 	g_CVDrvierCommonLog;

CUdpClientHandler::CUdpClientHandler(void)
{
	m_port = 0;
	m_moduleno = 0;
	m_multicast = false;
	m_transfermode = false;
	m_pUdpSocket = NULL;
	m_pMcastSocket = NULL;
	m_strServerIP = "";

	// 初始化回调函数相关变量
	m_pfnRecvCallback = NULL;
	m_lCallbackParam = 0;
	memset(m_szCallbackParam, 0x00, ICV_DEVICENAME_MAXLEN);
	m_pCallbackParam = NULL;
}

CUdpClientHandler::~CUdpClientHandler(void)
{
	DisConnect();
}

long CUdpClientHandler::SetConnectParam(char *szConnParam)
{
	if (NULL == szConnParam)
		return -1;

	// connparam="ip=*************;port=1234;multicast=0;transfermode=0"
	string strConnParam = szConnParam;

	// 获取IP地址
	int nPos = strConnParam.find("ip=");
	if(nPos != string::npos)
	{
		string strIP = strConnParam.substr(nPos + strlen("ip="));
		nPos = strIP.find(';');
		if(nPos != strIP.npos)
			strIP = strIP.substr(0, nPos);
		m_strServerIP = strIP;
	}
	else
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find ip parameter."), m_strDeviceName.c_str());
		return -1;
	}

	// 获取端口号
	nPos = strConnParam.find("port=");
	if(nPos == string::npos)
	{
        //设备 %s 作为UDPClinet进行连接, 找不到port参数
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find port parameter."), m_strDeviceName.c_str());
		return -1;
	}

	string strPortNo = strConnParam.substr(nPos + strlen("port="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	m_port = ::atoi(strPortNo.c_str());

	// 获取多播参数
	nPos = strConnParam.find("multicast=");
	if(nPos == string::npos)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find multicast parameter."), m_strDeviceName.c_str());
		return -1;
	}
	string strMulticast = strConnParam.substr(nPos + strlen("multicast="));
	nPos = strMulticast.find(';');
	if(nPos != strMulticast.npos)
		strMulticast = strMulticast.substr(0, nPos);

	m_multicast = ::atoi(strMulticast.c_str());

	// 获取缓冲区模式
	nPos = strConnParam.find("transfermode=");
	if(nPos == string::npos)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find transfermode parameter."), m_strDeviceName.c_str());
		return -1;
	}

	string strTransferMode = strConnParam.substr(nPos + strlen("transfermode="));
	nPos = strTransferMode.find(';');
	if(nPos != strTransferMode.npos)
		strTransferMode = strTransferMode.substr(0, nPos);

	m_transfermode = ::atoi(strTransferMode.c_str());

	// 获取模块序号（可选参数）
	nPos = strConnParam.find("moduleno=");
	if(nPos != string::npos)
	{
		string strModuleNo = strConnParam.substr(nPos + strlen("moduleno="));
		nPos = strModuleNo.find(';');
		if(nPos != strModuleNo.npos)
			strModuleNo = strModuleNo.substr(0, nPos);
		m_moduleno = ::atoi(strModuleNo.c_str());
	}

	return DRV_SUCCESS;
}

long CUdpClientHandler::Connect()
{
	// 如果套接字已经创建，先关闭
	if(m_pUdpSocket || m_pMcastSocket)
		DisConnect();

	try
	{
		// 设置远程地址
		m_remoteAddr.set(m_port, m_strServerIP.c_str());

		if(m_multicast)
		{
			// 创建组播套接字
			m_pMcastSocket = new ACE_SOCK_Dgram_Mcast();

			// 设置本地地址，绑定到任意可用端口
			m_localAddr.set((u_short)0, (ACE_UINT32)INADDR_ANY);

			// 打开组播套接字
			if(m_pMcastSocket->open(m_remoteAddr, NULL, 1) == -1)
			{
				CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s failed to open multicast socket"), m_strDeviceName.c_str());
				delete m_pMcastSocket;
				m_pMcastSocket = NULL;
				return -1;
			}

			// 加入组播组
			if(m_pMcastSocket->join(m_remoteAddr) == -1)
			{
				CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s failed to join multicast group %s:%d"),
					m_strDeviceName.c_str(), m_strServerIP.c_str(), m_port);
				m_pMcastSocket->close();
				delete m_pMcastSocket;
				m_pMcastSocket = NULL;
				return -1;
			}

			CV_INFO(g_CVDrvierCommonLog, "Device %s connected to multicast group %s:%d",
				m_strDeviceName.c_str(), m_strServerIP.c_str(), m_port);
		}
		else
		{
			// 创建单播套接字
			m_pUdpSocket = new ACE_SOCK_Dgram();

			// 设置本地地址，绑定到任意可用端口
			m_localAddr.set((u_short)0, (ACE_UINT32)INADDR_ANY);

			// 打开UDP套接字
			if(m_pUdpSocket->open(m_localAddr) == -1)
			{
				CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s failed to open UDP socket"), m_strDeviceName.c_str());
				delete m_pUdpSocket;
				m_pUdpSocket = NULL;
				return -1;
			}

			CV_INFO(g_CVDrvierCommonLog, "Device %s connected to UDP server %s:%d",
				m_strDeviceName.c_str(), m_strServerIP.c_str(), m_port);
		}

		return DRV_SUCCESS;
	}
	catch(...)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s connect failed with exception"), m_strDeviceName.c_str());
		DisConnect();
		return -1;
	}
}

long CUdpClientHandler::DisConnect()
{
	try
	{
		if(m_multicast && m_pMcastSocket)
		{
			// 离开组播组
			m_pMcastSocket->leave(m_remoteAddr);
			m_pMcastSocket->close();
			delete m_pMcastSocket;
			m_pMcastSocket = NULL;
			CV_INFO(g_CVDrvierCommonLog, "Device %s disconnected from multicast group", m_strDeviceName.c_str());
		}
		else if(!m_multicast && m_pUdpSocket)
		{
			m_pUdpSocket->close();
			delete m_pUdpSocket;
			m_pUdpSocket = NULL;
			CV_INFO(g_CVDrvierCommonLog, "Device %s disconnected from UDP server", m_strDeviceName.c_str());
		}

		return DRV_SUCCESS;
	}
	catch(...)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s disconnect failed with exception"), m_strDeviceName.c_str());
		return -1;
	}
}

long CUdpClientHandler::Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs)
{
	if(!pszSendBuf || nSendBytes <= 0)
		return -1;

	try
	{
		ACE_Time_Value tvTimeout;
		tvTimeout.msec(lTimeoutMs);

		long lSentBytes = 0;

		if(m_multicast && m_pMcastSocket)
		{
			// 组播发送 - 使用基类ACE_SOCK_Dgram的send方法
			lSentBytes = ((ACE_SOCK_Dgram*)m_pMcastSocket)->send(pszSendBuf, nSendBytes, m_remoteAddr, 0, &tvTimeout);
		}
		else if(!m_multicast && m_pUdpSocket)
		{
			// 单播发送
			lSentBytes = m_pUdpSocket->send(pszSendBuf, nSendBytes, m_remoteAddr, 0, &tvTimeout);
		}
		else
		{
			CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s socket not initialized"), m_strDeviceName.c_str());
			return -1;
		}

		if(lSentBytes != nSendBytes)
		{
			CV_WARN(g_CVDrvierCommonLog, -1, "Device %s sent %ld bytes, expected %ld bytes",
				m_strDeviceName.c_str(), lSentBytes, nSendBytes);
		}

		return lSentBytes;
	}
	catch(...)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s send failed with exception"), m_strDeviceName.c_str());
		return -1;
	}
}

long CUdpClientHandler::Recv(char* szRecvBuf, long nRecvBytes, long lTimeoutMs)
{
	if(!szRecvBuf || nRecvBytes <= 0)
		return -1;

	try
	{
		ACE_Time_Value tvTimeout;
		tvTimeout.msec(lTimeoutMs);
		ACE_INET_Addr remoteAddr;

		long lRecvBytes = 0;

		if(m_multicast && m_pMcastSocket)
		{
			// 组播接收
			lRecvBytes = m_pMcastSocket->recv(szRecvBuf, nRecvBytes, remoteAddr, 0, &tvTimeout);
		}
		else if(!m_multicast && m_pUdpSocket)
		{
			// 单播接收
			lRecvBytes = m_pUdpSocket->recv(szRecvBuf, nRecvBytes, remoteAddr, 0, &tvTimeout);
		}
		else
		{
			CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s socket not initialized"), m_strDeviceName.c_str());
			return -1;
		}

		// 对于单播，可以验证发送方地址是否匹配
		if(!m_multicast && lRecvBytes > 0)
		{
			char szRemoteIP[RECV_BUFFER_LEN] = {'\0'};
			remoteAddr.addr_to_string(szRemoteIP, RECV_BUFFER_LEN);

			// 检查是否来自预期的服务器
			if(!m_strServerIP.empty() && strstr(szRemoteIP, m_strServerIP.c_str()) == NULL)
			{
				CV_WARN(g_CVDrvierCommonLog, -1, "Device %s received data from unexpected source %s, expected %s",
					m_strDeviceName.c_str(), szRemoteIP, m_strServerIP.c_str());
				return -1;
			}
		}

		return lRecvBytes;
	}
	catch(...)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s recv failed with exception"), m_strDeviceName.c_str());
		return -1;
	}
}

long CUdpClientHandler::Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS)
{
	if(!szReadBuf || lExactReadBufLen <= 0)
		return -1;

	try
	{
		ACE_Time_Value tvTimeout;
		tvTimeout.msec(lTimeOutMS);
		ACE_INET_Addr remoteAddr;

		long lRecvBytes = 0;

		if(m_multicast && m_pMcastSocket)
		{
			// 组播接收
			lRecvBytes = m_pMcastSocket->recv(szReadBuf, lExactReadBufLen, remoteAddr, 0, &tvTimeout);
		}
		else if(!m_multicast && m_pUdpSocket)
		{
			// 单播接收
			lRecvBytes = m_pUdpSocket->recv(szReadBuf, lExactReadBufLen, remoteAddr, 0, &tvTimeout);
		}
		else
		{
			CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s socket not initialized"), m_strDeviceName.c_str());
			return -1;
		}

		return lRecvBytes;
	}
	catch(...)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s recv_n failed with exception"), m_strDeviceName.c_str());
		return -1;
	}
}

void CUdpClientHandler::ClearDeviceRecvBuffer()
{
	char szBuffer[RECV_BUFFER_LEN] = {'\0'};
	int nRecvBytes = 0;

	// 清空接收缓冲区，使用短超时时间
	while((nRecvBytes = Recv(szBuffer, sizeof(szBuffer), 100)) > 0)
	{
		if (nRecvBytes < sizeof(szBuffer))
			break;
	}
}

long CUdpClientHandler::Start()
{
	return Connect();
}

long CUdpClientHandler::Stop()
{
	return DisConnect();
}

void CUdpClientHandler::SetRecvCallBackFunc(PFN_CVCommRecvCallBack pfnRecvFunc, void* pCallbackParam, long lCallbackParam, char *pszCallbackParam)
{
	m_pfnRecvCallback = pfnRecvFunc;
	m_pCallbackParam = pCallbackParam;
	m_lCallbackParam = lCallbackParam;
	if(pszCallbackParam)
		memcpy(m_szCallbackParam, pszCallbackParam, ICV_DEVICENAME_MAXLEN);
}
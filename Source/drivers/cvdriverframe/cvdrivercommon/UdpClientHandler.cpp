#include "UdpClientHandler.h"
#include "driversdk/cvdrivercommon.h"
#include "ace/Time_Value.h"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include <ace/ACE.h>
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "ace/Message_Block.h"

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING

extern string	g_strDrvName;
extern CCVLog 	g_CVDrvierCommonLog;

CUdpClientHandler::CUdpClientHandler(void)
{
	m_port = 0;
}

CUdpClientHandler::~CUdpClientHandler(void)
{
	DisConnect();
}

long CUdpClientHandler::SetConnectParam(char *szConnParam)
{
	if (NULL == szConnParam)
		return -1;

	// connparam="moduleno=100;port=1234;byteorder=1;multicast=0;transfermode=0"
	// 获取端口号
	string strConnParam = szConnParam;
	int nPos = strConnParam.find("port=");
	if(nPos == string::npos)
	{
        //设备 %s 作为UDPClinet进行连接, 找不到port参数
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find port parameter."), m_strDeviceName.c_str());
		return -1;
	}
    
	string strPortNo = strConnParam.substr(nPos + strlen("port="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	m_port = ::atoi(strPortNo.c_str());

	// 获取多播参数
	nPos = strConnParam.find("multicast=");
	if(nPos == string::npos)
	{ 
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find multicast parameter."), m_strDeviceName.c_str());
		return -1;
	}
	string strMulticast = strConnParam.substr(nPos + strlen("multicast="));
	nPos = strMulticast.find(';');
	if(nPos != strMulticast.npos)
		strMulticast = strMulticast.substr(0, nPos);

	m_multicast = ::atoi(strMulticast.c_str());

	// 获取缓冲区模式
	nPos = strConnParam.find("transfermode=");
	if(nPos == string::npos)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find transfermode parameter."), m_strDeviceName.c_str());
		return -1;
	}

	string strTransferMode = strConnParam.substr(nPos + strlen("transfermode="));
	nPos = strTransferMode.find(';');
	if(nPos != strTransferMode.npos)
		strTransferMode = strTransferMode.substr(0, nPos);

	m_transfermode = ::atoi(strTransferMode.c_str());

	return DRV_SUCCESS;
}

long CUdpClientHandler::Connect()
{
	// 创建多播套接字
	if(m_multicast)
	{

	}
	else
	{

	}
	return DRV_SUCCESS;
}

long CUdpClientHandler::DisConnect()
{
	return DRV_SUCCESS;
}

//不实现
long CUdpClientHandler::Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs)
{
	return DRV_SUCCESS;
}

long CUdpClientHandler::Recv(char* szRecvBuf, long nRecvBytes, long lTimeoutMs)
{
	
}

long CUdpClientHandler::Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS)
{
	
}

void CUdpClientHandler::ClearDeviceRecvBuffer()
{
	
}

long CUdpClientHandler::Start()
{
	return Connect();
}

long CUdpClientHandler::Stop()

{
	return DisConnect();
}
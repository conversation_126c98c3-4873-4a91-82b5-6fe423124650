#include <stdlib.h>
#include <memory.h>
#include "ace/OS_NS_sys_time.h"
#include "udpdrv.h"
#include "math.h"

CCVLog g_CVLogUdp;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

map<DRVHANDLE, map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUdp.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRV<PERSON><PERSON>LE hDriver)
{
	return 0;
}

CVDRIVER_EXPORTS long UnInitialize()
{
    g_CVLogUdp.StopLogThread();
    return DRV_SUCCESS;
}

void CheckBlockStatus(DRVHANDLE hDevice, DRVHANDLE hDatablock, long lSuccess)
{
	if(lSuccess == DRV_SUCCESS)
		Drv_SetUserData(hDatablock, 0, 0);
	else
	{
		long lFailCountRecent = Drv_GetUserData(hDatablock, 0);
		if(lFailCountRecent > 3)	// 最近失败次数
		{
			Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);
			Drv_SetUserData(hDatablock, 0, 0); // 避免计数太大导致循环
		}
		else
			Drv_SetUserData(hDatablock, 0, lFailCountRecent + 1);
	}
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);

	// 从设备接收消息
	long lCurRecvLen = 0;
	char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

	unsigned short nPackageLen = 0;
	CV_TRACE(g_CVLogUdp, "Drv_RecvFromDevice Device %s  receive start ", pDevice->pszName);
	long lRecvBytes = Drv_RecvFromDevice(hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
	CV_TRACE(g_CVLogUdp, "Drv_RecvFromDevice Device %s  receive end   lRecvBytes = %d ", pDevice->pszName, lRecvBytes);

	ACE_Time_Value* pLastRecvDataTime = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 1);
	ACE_Time_Value* pDisconnectTimeout = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 0);
	ACE_Time_Value tvCurrentTime = ACE_OS::gettimeofday();
	if(lRecvBytes <= 0)
	{
		//本次没有接收到数据，但是并没有到超时断开连接的时间
		if (tvCurrentTime - *pLastRecvDataTime < *pDisconnectTimeout)
		{
			return DRV_SUCCESS;
		}

		//接收超时，说明链路上一直没有数据，置该链路上的所有数据块质量为BAD
		for (std::map<DRVHANDLE, CVDATABLOCK*>::iterator itDB = g_mapDataBlocks[hDevice].begin(); itDB != g_mapDataBlocks[hDevice].end(); ++itDB)
		{
			Drv_UpdateBlockStatus(hDevice, itDB->first, DATA_STATUS_TIMEOUT_FAILURE);
		}

		CV_WARN(g_CVLogUdp, -1, "Disconnect with device %s because of receive timeout %d ms", pDevice->pszName, pDevice->nRecvTimeout);
		long lRet = Drv_DisconnectDevice(hDevice);
		CV_CHECK_FAIL_LOG(g_CVLogUdp, lRet, lRet, "Drv_DisconnectDevice");
		*pLastRecvDataTime = tvCurrentTime;
		Drv_SetUserDataPtr(hDevice, 1, pLastRecvDataTime);

		return DRV_SUCCESS;
	}
	else if (lRecvBytes <= UDP_PACKAGE_HEADER_LEN)
	{
		//不合法的包长度
		CV_WARN(g_CVLogUdp, -1, "Device %s receive package of invalid size %d", pDevice->pszName, lRecvBytes);
		return DRV_SUCCESS;
	}

	

}

/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

 /*  初始化数据块请求计数为0 .*/
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}
